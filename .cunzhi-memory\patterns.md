# 常用模式和最佳实践


## 代码注入文件

代码注入文件 `d:\augment-qushi/inject-code.js`，需要将其内容注入到 VSCode 插件的输出文件中。具体操作流程如下：

1. **源文件**：`d:\augment-qushi/inject-code.js` - 这是需要注入的代码
2. **目标文件**：`D:\augment-qushi\0.522.0\extension\out\extension.js` - VSCode 插件的主要输出文件
3. **注入位置**：将 inject-code.js 的内容复制到 extension.js 文件的开头部分
4. **注入方式**：手动复制粘贴（我会自己手动操作这一步）
5. **最终目标**：完成注入后，打包成 VSCode 的 .vsix 文件，安装并启用插件后，注入的代码会自动加载执行
6. **规则**：不允许修改 VSCode 插件的源代码，只能修改注入的代码。

- 在inject-code.js中实现HTML注入悬浮窗口功能，采用轻量级DOM注入方案，包含存储管理器、悬浮窗口创建器、内容展示管理器和事件处理器四个核心模块，遵循KISS/YAGNI/SOLID原则
