/**
 * 微信公众号菜单扩展
 * 功能：在VSCode插件中添加微信公众号信息显示功能
 * 作者：煎饼果子卷AI
 */

const vscode = require('vscode');

/**
 * 激活扩展
 * @param {vscode.ExtensionContext} context 
 */
function activate(context) {
    console.log('微信公众号菜单扩展已激活');

    // 注册显示微信公众号信息的命令
    const showWechatInfoCommand = vscode.commands.registerCommand('vscode-augment.showWechatInfo', async () => {
        try {
            // 显示信息对话框，包含复制按钮
            const result = await vscode.window.showInformationMessage(
                '公众号：@煎饼果子卷AI',
                {
                    modal: true,
                    detail: '关注微信公众号获取最新版本和技术支持\n\n点击"复制"按钮将公众号名称复制到剪贴板'
                },
                '复制',
                '关闭'
            );

            // 处理用户选择
            if (result === '复制') {
                // 复制到剪贴板
                await vscode.env.clipboard.writeText('@煎饼果子卷AI');
                
                // 显示复制成功提示
                vscode.window.showInformationMessage('已复制"@煎饼果子卷AI"到剪贴板！');
                
                console.log('用户复制了微信公众号信息');
            } else {
                console.log('用户关闭了微信公众号信息对话框');
            }
        } catch (error) {
            console.error('显示微信公众号信息时出错:', error);
            vscode.window.showErrorMessage('显示公众号信息时出错: ' + error.message);
        }
    });

    // 将命令添加到上下文订阅中
    context.subscriptions.push(showWechatInfoCommand);

    console.log('微信公众号菜单命令已注册');
}

/**
 * 停用扩展
 */
function deactivate() {
    console.log('微信公众号菜单扩展已停用');
}

module.exports = {
    activate,
    deactivate
};
