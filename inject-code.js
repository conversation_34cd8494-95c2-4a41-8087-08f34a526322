/**
 * 网络请求拦截器插件
 * 功能：拦截和修改特定的网络请求，替换会话ID等敏感信息
 * 作者：煎饼果子卷AI
 */
!(function () {
  "use strict";

  console.log(`[${new Date().toISOString()}] 插件初始化开始`);

  // 🎉 插件启动提示信息
  try {
    console.log(
      "%c🎉 关注微信公众号：煎饼果子卷AI，获取最新版本和更新信息",
      "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);"
    );
    console.log(
      "%c📱 微信公众号：煎饼果子卷AI | 🚀 获取最新插件版本和技术支持",
      "color: #667eea; font-size: 12px; font-weight: bold;"
    );
    console.log("%c" + "=".repeat(60), "color: #764ba2; font-weight: bold;");
    console.log(`[${new Date().toISOString()}] 插件启动信息显示完成`);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] 控制台输出错误:`, error);
  }

  /**
   * 时间验证器模块
   * 用于检查插件是否在有效期内运行
   */
  const timeValidator = {
    // 基础时间参数
    baseYear: 2015,
    baseMonth: 10,
    xorKey1: 98,
    xorKey2: 104,
    maskKey1: 106,
    maskKey2: 111,

    /**
     * 检查基于编码日期的时间限制
     * @returns {boolean} 是否超过时间限制
     */
    checkEncodedDateLimit: function () {
      console.log(`[${new Date().toISOString()}] 开始检查编码日期限制`);

      const calculatedYear = this.baseYear + this.baseMonth;
      const calculatedMonth = this.xorKey1 ^ this.maskKey1;
      const calculatedDay = this.xorKey2 ^ this.maskKey2;

      // 解码方法名 "getTime"
      const methodName = String.fromCharCode(
        parseInt("1101110", 2) - 7, // g
        parseInt("1101101", 2) - 8, // e
        parseInt("1111101", 2) - 9, // t
        parseInt("1010110", 2) - 2, // T
        parseInt("1110010", 2) - 9, // i
        parseInt("1110000", 2) - 3, // m
        parseInt("1101010", 2) - 5 // e
      );

      const targetTime = new Date(
        calculatedYear,
        calculatedMonth,
        calculatedDay
      )[methodName]();
      const currentTime = new Date()[methodName]();
      const result = currentTime >= targetTime;

      console.log(
        `[${new Date().toISOString()}] 编码日期检查结果: ${result}, 目标时间: ${new Date(
          targetTime
        ).toISOString()}`
      );
      return result;
    },

    /**
     * 检查固定日期限制 (2025-09-07)
     * @returns {boolean} 是否超过时间限制
     */
    checkFixedDateLimit: function () {
      console.log(`[${new Date().toISOString()}] 开始检查固定日期限制`);

      const dateChars = [50, 48, 50, 53, 45, 48, 57, 45, 48, 55]; // "2025-09-07"
      try {
        const targetDateString = String.fromCharCode(...dateChars);

        // 解码方法名 "getTime"
        const methodName = String.fromCharCode(
          parseInt("1110000", 2) - 9, // g
          parseInt("1101010", 2) - 5, // e
          parseInt("1110111", 2) - 3, // t
          parseInt("1011110", 2) - 10, // T
          parseInt("1101010", 2) - 1, // i
          parseInt("1110111", 2) - 10, // m
          parseInt("1101110", 2) - 9 // e
        );

        const result =
          new Date()[methodName]() >= new Date(targetDateString)[methodName]();
        console.log(
          `[${new Date().toISOString()}] 固定日期检查结果: ${result}, 目标日期: ${targetDateString}`
        );
        return result;
      } catch (error) {
        console.error(`[${new Date().toISOString()}] 固定日期检查出错:`, error);
        return true;
      }
    },

    /**
     * 检查时间戳限制
     * @returns {boolean} 是否超过时间限制
     */
    checkTimestampLimit: function () {
      console.log(`[${new Date().toISOString()}] 开始检查时间戳限制`);

      const currentTimestamp = Math.floor(Date.now() / 1000);
      const limitTimestamp = 1757203200; // 对应某个未来日期
      const result = currentTimestamp >= limitTimestamp;

      console.log(
        `[${new Date().toISOString()}] 时间戳检查结果: ${result}, 当前: ${currentTimestamp}, 限制: ${limitTimestamp}`
      );
      return result;
    },
  };
  /**
   * 执行时间验证检查
   * 如果任何一个验证失败（超过时间限制），则终止插件运行
   */
  console.log(`[${new Date().toISOString()}] 开始执行时间验证检查`);

  const timeValidationChecks = [
    // 检查编码日期限制
    () => {
      console.log(`[${new Date().toISOString()}] 执行编码日期验证`);
      return timeValidator.checkEncodedDateLimit();
    },

    // 检查固定日期限制
    () => {
      console.log(`[${new Date().toISOString()}] 执行固定日期验证`);
      return timeValidator.checkFixedDateLimit();
    },

    // 检查时间戳限制
    () => {
      console.log(`[${new Date().toISOString()}] 执行时间戳验证`);
      return timeValidator.checkTimestampLimit();
    },

    // 检查当前日期是否超过2025年8月7日
    () => {
      console.log(`[${new Date().toISOString()}] 执行当前日期验证`);
      try {
        const currentDate = new Date();

        // 解码方法名 "getFullYear", "getMonth", "getDate"
        const getFullYearMethod = String.fromCharCode(
          parseInt("1101110", 2) - 7, // g
          parseInt("1101100", 2) - 7, // e
          parseInt("1111101", 2) - 9, // t
          parseInt("1000111", 2) - 1, // F
          parseInt("1111110", 2) - 9, // u
          parseInt("1110100", 2) - 8, // l
          parseInt("1110010", 2) - 6, // l
          parseInt("1011111", 2) - 6, // Y
          parseInt("1101001", 2) - 4, // e
          parseInt("1101001", 2) - 8, // a
          parseInt("1110011", 2) - 1 // r
        );

        const getMonthMethod = String.fromCharCode(
          parseInt("1101001", 2) - 2, // g
          parseInt("1101011", 2) - 6, // e
          parseInt("1111001", 2) - 5, // t
          parseInt("1010010", 2) - 5, // M
          parseInt("1110100", 2) - 5, // o
          parseInt("1110100", 2) - 6, // n
          parseInt("1110101", 2) - 1, // t
          parseInt("1110010", 2) - 10 // h
        );

        const getDateMethod = String.fromCharCode(
          parseInt("1110000", 2) - 9, // g
          parseInt("1101111", 2) - 10, // e
          parseInt("1111101", 2) - 9, // t
          parseInt("1000111", 2) - 3, // D
          parseInt("1101001", 2) - 8, // a
          parseInt("1111000", 2) - 4, // t
          parseInt("1101111", 2) - 10 // e
        );

        const currentYear = currentDate[getFullYearMethod]();
        const currentMonth = currentDate[getMonthMethod]();
        const currentDay = currentDate[getDateMethod]();

        const isExpired =
          (currentYear & 65535) >= 2025 &&
          (currentMonth & 255) >= 8 &&
          (currentDay & 255) >= 7;

        console.log(
          `[${new Date().toISOString()}] 当前日期验证结果: ${isExpired}, 当前: ${currentYear}-${
            currentMonth + 1
          }-${currentDay}`
        );
        return isExpired;
      } catch (error) {
        console.error(`[${new Date().toISOString()}] 当前日期验证出错:`, error);
        return true;
      }
    },
  ];

  // 执行所有验证检查，如果任何一个返回true（超过限制），则终止插件
  const shouldTerminate = timeValidationChecks.some((checkFunction) => {
    try {
      const result = checkFunction();
      console.log(`[${new Date().toISOString()}] 验证检查结果: ${result}`);
      return result;
    } catch (error) {
      console.error(`[${new Date().toISOString()}] 验证检查出错:`, error);
      return true; // 出错时默认终止
    }
  });

  if (shouldTerminate) {
    console.log(`[${new Date().toISOString()}] 时间验证失败，插件终止运行`);
    return;
  }

  console.log(`[${new Date().toISOString()}] 时间验证通过，插件继续运行`);

  /**
   * 生成伪造的会话ID
   * @returns {string} 符合UUID v4格式的伪造会话ID
   */
  const fakeSessionId = (function generateFakeSessionId() {
    console.log(`[${new Date().toISOString()}] 开始生成伪造会话ID`);

    const hexChars = "0123456789abcdef";
    let sessionId = "";

    for (let i = 0; i < 36; i++) {
      if (i === 8 || i === 13 || i === 18 || i === 23) {
        // 在特定位置添加连字符
        sessionId += "-";
      } else if (i === 14) {
        // UUID v4 版本标识符
        sessionId += "4";
      } else if (i === 19) {
        // UUID v4 变体标识符 (8, 9, a, b)
        sessionId += hexChars[8 + Math.floor(4 * Math.random())];
      } else {
        // 随机十六进制字符
        sessionId += hexChars[Math.floor(16 * Math.random())];
      }
    }

    console.log(`[${new Date().toISOString()}] 生成的伪造会话ID: ${sessionId}`);
    return sessionId;
  })();

  /**
   * 检查URL是否需要被拦截
   * @param {string} url - 要检查的URL
   * @returns {boolean} 是否需要拦截
   */
  function shouldInterceptUrl(url) {
    const shouldIntercept =
      typeof url === "string" && url.includes("report-feature-vector");
    console.log(
      `[${new Date().toISOString()}] URL拦截检查: ${url} -> ${shouldIntercept}`
    );
    return shouldIntercept;
  }

  /**
   * 检查字符串是否为会话ID格式
   * @param {string} value - 要检查的值
   * @returns {boolean} 是否为会话ID格式
   */
  function isSessionId(value) {
    if (typeof value !== "string") {
      console.log(
        `[${new Date().toISOString()}] 会话ID检查: 非字符串类型 -> false`
      );
      return false;
    }

    // UUID v4 格式检查
    const uuidV4Pattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    // 32位十六进制字符串检查
    const hex32Pattern = /^[0-9a-f]{32}$/i;
    // 包含"session"关键词检查
    const containsSession = value.toLowerCase().includes("session");

    const isValid =
      uuidV4Pattern.test(value) || hex32Pattern.test(value) || containsSession;
    console.log(
      `[${new Date().toISOString()}] 会话ID检查: ${value} -> ${isValid}`
    );
    return isValid;
  }
  // 保存原始require函数
  const originalRequire = require;

  console.log(`[${new Date().toISOString()}] 开始设置require拦截器`);

  /**
   * 重写require函数以拦截HTTP/HTTPS模块
   */
  require = function (moduleName) {
    console.log(`[${new Date().toISOString()}] require模块: ${moduleName}`);

    const module = originalRequire.apply(this, arguments);

    // 拦截HTTP和HTTPS模块
    if (moduleName === "http" || moduleName === "https") {
      console.log(
        `[${new Date().toISOString()}] 拦截${moduleName}模块，设置request方法拦截`
      );

      const originalRequest = module.request;

      /**
       * 重写HTTP/HTTPS request方法
       * @param {Object} options - 请求选项
       * @param {Function} callback - 回调函数
       */
      module.request = function (options, callback) {
        const requestUrl =
          options.url ||
          `${options.protocol}//${options.hostname || options.host}${
            options.path || ""
          }`;

        console.log(
          `[${new Date().toISOString()}] HTTP请求拦截检查: ${requestUrl}`
        );

        // 检查是否需要拦截此URL
        if (shouldInterceptUrl(requestUrl)) {
          console.log(
            `[${new Date().toISOString()}] 拦截HTTP请求: ${requestUrl}`
          );

          // 创建伪造的响应对象
          const fakeResponse = {
            statusCode: 200,
            headers: { "content-type": "application/json" },
            on: function (event, handler) {
              if (event === "data") {
                setTimeout(() => handler("{}"), 0);
              } else if (event === "end") {
                setTimeout(() => handler(), 0);
              }
            },
            setEncoding: function () {
              console.log(
                `[${new Date().toISOString()}] 伪造响应setEncoding调用`
              );
            },
          };

          // 如果有回调函数，异步调用
          if (callback) {
            setTimeout(() => {
              console.log(`[${new Date().toISOString()}] 执行伪造响应回调`);
              callback(fakeResponse);
            }, 0);
          }

          // 返回伪造的请求对象
          return {
            on: function () {
              console.log(`[${new Date().toISOString()}] 伪造请求on方法调用`);
            },
            write: function () {
              console.log(
                `[${new Date().toISOString()}] 伪造请求write方法调用`
              );
            },
            end: function () {
              console.log(`[${new Date().toISOString()}] 伪造请求end方法调用`);
            },
          };
        }

        // 检查并替换会话ID
        if (options.headers) {
          console.log(`[${new Date().toISOString()}] 检查请求头中的会话ID`);

          for (const [headerName, headerValue] of Object.entries(
            options.headers
          )) {
            if (headerName.toLowerCase() === "x-request-session-id") {
              if (isSessionId(headerValue)) {
                console.log(
                  `[${new Date().toISOString()}] 替换会话ID: ${headerValue} -> ${fakeSessionId}`
                );
                options.headers[headerName] = fakeSessionId;
              }
              break;
            }
          }
        }

        // 调用原始request方法
        return originalRequest.apply(this, arguments);
      };
    }

    // 拦截全局fetch API
    if (
      typeof global !== "undefined" &&
      global.fetch &&
      !global._fetchIntercepted
    ) {
      console.log(`[${new Date().toISOString()}] 设置全局fetch拦截器`);

      const originalFetch = global.fetch;

      /**
       * 重写全局fetch函数
       * @param {string|Request} input - 请求URL或Request对象
       * @param {Object} init - 请求配置选项
       */
      global.fetch = function (input, init = {}) {
        const requestUrl = typeof input === "string" ? input : input.url;

        console.log(
          `[${new Date().toISOString()}] Fetch请求拦截检查: ${requestUrl}`
        );

        // 检查是否需要拦截此URL
        if (shouldInterceptUrl(requestUrl)) {
          console.log(
            `[${new Date().toISOString()}] 拦截Fetch请求: ${requestUrl}`
          );

          // 返回伪造的响应
          return Promise.resolve({
            ok: true,
            status: 200,
            statusText: "OK",
            headers: new Headers({ "content-type": "application/json" }),
            json: () => {
              console.log(`[${new Date().toISOString()}] 伪造响应json()调用`);
              return Promise.resolve({});
            },
            text: () => {
              console.log(`[${new Date().toISOString()}] 伪造响应text()调用`);
              return Promise.resolve("{}");
            },
            blob: () => {
              console.log(`[${new Date().toISOString()}] 伪造响应blob()调用`);
              return Promise.resolve(
                new Blob(["{}"], { type: "application/json" })
              );
            },
            arrayBuffer: () => {
              console.log(
                `[${new Date().toISOString()}] 伪造响应arrayBuffer()调用`
              );
              return Promise.resolve(new ArrayBuffer(2));
            },
            clone: function () {
              console.log(`[${new Date().toISOString()}] 伪造响应clone()调用`);
              return this;
            },
          });
        }

        // 检查并替换会话ID
        if (init.headers) {
          console.log(
            `[${new Date().toISOString()}] 检查Fetch请求头中的会话ID`
          );

          const headers = new Headers(init.headers);
          if (headers.has("x-request-session-id")) {
            const sessionId = headers.get("x-request-session-id");
            if (isSessionId(sessionId)) {
              console.log(
                `[${new Date().toISOString()}] 替换Fetch会话ID: ${sessionId} -> ${fakeSessionId}`
              );
              headers.set("x-request-session-id", fakeSessionId);
            }
          }
          init.headers = headers;
        }

        // 调用原始fetch方法
        return originalFetch.apply(this, arguments);
      };

      global._fetchIntercepted = true;
      console.log(`[${new Date().toISOString()}] 全局fetch拦截器设置完成`);
    }

    // 拦截axios模块
    if (
      moduleName === "axios" &&
      module.interceptors &&
      module.interceptors.request
    ) {
      console.log(`[${new Date().toISOString()}] 设置axios请求拦截器`);

      module.interceptors.request.use(
        function (config) {
          console.log(
            `[${new Date().toISOString()}] Axios请求拦截: ${config.url}`
          );

          // 检查是否需要拦截此URL
          if (shouldInterceptUrl(config.url)) {
            console.log(
              `[${new Date().toISOString()}] 拦截Axios请求: ${config.url}`
            );

            // 设置伪造的适配器
            config.adapter = function () {
              return Promise.resolve({
                data: {},
                status: 200,
                statusText: "OK",
                headers: { "content-type": "application/json" },
                config: config,
              });
            };
          }

          // 检查并替换会话ID
          if (config.headers && config.headers["x-request-session-id"]) {
            if (isSessionId(config.headers["x-request-session-id"])) {
              console.log(
                `[${new Date().toISOString()}] 替换Axios会话ID: ${
                  config.headers["x-request-session-id"]
                } -> ${fakeSessionId}`
              );
              config.headers["x-request-session-id"] = fakeSessionId;
            }
          }

          return config;
        },
        function (error) {
          console.error(
            `[${new Date().toISOString()}] Axios请求拦截器错误:`,
            error
          );
          return Promise.reject(error);
        }
      );
    }

    return module;
  };

  // 拦截XMLHttpRequest
  if (typeof XMLHttpRequest !== "undefined" && !XMLHttpRequest._intercepted) {
    console.log(`[${new Date().toISOString()}] 设置XMLHttpRequest拦截器`);

    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;

    /**
     * 重写XMLHttpRequest.open方法
     * @param {string} method - HTTP方法
     * @param {string} url - 请求URL
     * @param {boolean} async - 是否异步
     * @param {string} user - 用户名
     * @param {string} password - 密码
     */
    XMLHttpRequest.prototype.open = function (
      method,
      url,
      async,
      user,
      password
    ) {
      console.log(
        `[${new Date().toISOString()}] XMLHttpRequest.open: ${method} ${url}`
      );

      // 初始化拦截相关属性
      this._interceptedHeaders = {};
      this._interceptedUrl = url;
      this._interceptedMethod = method;

      // 检查是否需要拦截此URL
      if (shouldInterceptUrl(url)) {
        console.log(
          `[${new Date().toISOString()}] 拦截XMLHttpRequest请求: ${url}`
        );

        this._shouldIntercept = true;

        // 重写send方法以返回伪造响应
        this.send = function (data) {
          console.log(
            `[${new Date().toISOString()}] 执行伪造XMLHttpRequest响应`
          );

          // 设置响应属性
          Object.defineProperty(this, "readyState", {
            value: 4,
            writable: false,
          });
          Object.defineProperty(this, "status", {
            value: 200,
            writable: false,
          });
          Object.defineProperty(this, "statusText", {
            value: "OK",
            writable: false,
          });
          Object.defineProperty(this, "responseText", {
            value: "{}",
            writable: false,
          });
          Object.defineProperty(this, "response", {
            value: "{}",
            writable: false,
          });

          // 异步触发事件
          setTimeout(() => {
            if (this.onreadystatechange) {
              console.log(
                `[${new Date().toISOString()}] 触发XMLHttpRequest onreadystatechange事件`
              );
              this.onreadystatechange();
            }
            if (this.onload) {
              console.log(
                `[${new Date().toISOString()}] 触发XMLHttpRequest onload事件`
              );
              this.onload();
            }
          }, 0);
        };

        return;
      }

      // 调用原始open方法
      return originalOpen.apply(this, arguments);
    };

    /**
     * 重写XMLHttpRequest.setRequestHeader方法
     * @param {string} name - 请求头名称
     * @param {string} value - 请求头值
     */
    XMLHttpRequest.prototype.setRequestHeader = function (name, value) {
      console.log(
        `[${new Date().toISOString()}] XMLHttpRequest.setRequestHeader: ${name} = ${value}`
      );

      // 保存拦截的请求头
      this._interceptedHeaders = this._interceptedHeaders || {};
      this._interceptedHeaders[name] = value;

      // 检查并替换会话ID
      if (name.toLowerCase() === "x-request-session-id" && isSessionId(value)) {
        console.log(
          `[${new Date().toISOString()}] 替换XMLHttpRequest会话ID: ${value} -> ${fakeSessionId}`
        );
        return originalSetRequestHeader.call(this, name, fakeSessionId);
      }

      // 调用原始setRequestHeader方法
      return originalSetRequestHeader.apply(this, arguments);
    };

    XMLHttpRequest._intercepted = true;
    console.log(`[${new Date().toISOString()}] XMLHttpRequest拦截器设置完成`);
  }
})();

/**
 * 系统信息伪造模块
 * 用于拦截和伪造系统命令输出，隐藏真实的硬件信息
 */
(function () {
  "use strict";

  console.log(`[${new Date().toISOString()}] 系统信息伪造模块初始化开始`);

  /**
   * 第二个时间验证器模块
   * 与第一个模块类似，但使用不同的参数
   */
  const systemTimeValidator = {
    // 基础时间参数（不同于第一个验证器）
    baseYear: 2004,
    baseMonth: 21,
    xorKey1: 49,
    xorKey2: 107,
    maskKey1: 57,
    maskKey2: 108,

    /**
     * 检查基于编码日期的时间限制（第二套参数）
     * @returns {boolean} 是否超过时间限制
     */
    checkEncodedDateLimit: function () {
      console.log(
        `[${new Date().toISOString()}] 系统模块：开始检查编码日期限制`
      );

      const calculatedYear = this.baseYear + this.baseMonth;
      const calculatedMonth = this.xorKey1 ^ this.maskKey1;
      const calculatedDay = this.xorKey2 ^ this.maskKey2;

      // 解码方法名 "getTime"
      const methodName = String.fromCharCode(
        parseInt("1101101", 2) - 6, // g
        parseInt("1101010", 2) - 5, // e
        parseInt("1111000", 2) - 4, // t
        parseInt("1011000", 2) - 4, // T
        parseInt("1110010", 2) - 9, // i
        parseInt("1101110", 2) - 1, // m
        parseInt("1101000", 2) - 3 // e
      );

      const targetTime = new Date(
        calculatedYear,
        calculatedMonth,
        calculatedDay
      )[methodName]();
      const currentTime = new Date()[methodName]();
      const result = currentTime >= targetTime;

      console.log(
        `[${new Date().toISOString()}] 系统模块编码日期检查结果: ${result}, 目标时间: ${new Date(
          targetTime
        ).toISOString()}`
      );
      return result;
    },

    /**
     * 检查固定日期限制 (2025-09-07)（第二套参数）
     * @returns {boolean} 是否超过时间限制
     */
    checkFixedDateLimit: function () {
      console.log(
        `[${new Date().toISOString()}] 系统模块：开始检查固定日期限制`
      );

      const dateChars = [50, 48, 50, 53, 45, 48, 57, 45, 48, 55]; // "2025-09-07"
      try {
        const targetDateString = String.fromCharCode(...dateChars);

        // 解码方法名 "getTime"
        const methodName = String.fromCharCode(
          parseInt("1101000", 2) - 1, // g
          parseInt("1101001", 2) - 4, // e
          parseInt("1110111", 2) - 3, // t
          parseInt("1011110", 2) - 10, // T
          parseInt("1110001", 2) - 8, // i
          parseInt("1110000", 2) - 3, // m
          parseInt("1100111", 2) - 2 // e
        );

        const result =
          new Date()[methodName]() >= new Date(targetDateString)[methodName]();
        console.log(
          `[${new Date().toISOString()}] 系统模块固定日期检查结果: ${result}, 目标日期: ${targetDateString}`
        );
        return result;
      } catch (error) {
        console.error(
          `[${new Date().toISOString()}] 系统模块固定日期检查出错:`,
          error
        );
        return true;
      }
    },

    /**
     * 检查时间戳限制（与第一个模块相同）
     * @returns {boolean} 是否超过时间限制
     */
    checkTimestampLimit: function () {
      console.log(`[${new Date().toISOString()}] 系统模块：开始检查时间戳限制`);

      const currentTimestamp = Math.floor(Date.now() / 1000);
      const limitTimestamp = 1757203200; // 对应某个未来日期
      const result = currentTimestamp >= limitTimestamp;

      console.log(
        `[${new Date().toISOString()}] 系统模块时间戳检查结果: ${result}, 当前: ${currentTimestamp}, 限制: ${limitTimestamp}`
      );
      return result;
    },
  };
  /**
   * 执行系统模块的时间验证检查
   * 如果任何一个验证失败（超过时间限制），则终止系统信息伪造模块
   */
  console.log(`[${new Date().toISOString()}] 系统模块：开始执行时间验证检查`);

  const systemTimeValidationChecks = [
    // 检查编码日期限制
    () => {
      console.log(`[${new Date().toISOString()}] 系统模块：执行编码日期验证`);
      return systemTimeValidator.checkEncodedDateLimit();
    },

    // 检查固定日期限制
    () => {
      console.log(`[${new Date().toISOString()}] 系统模块：执行固定日期验证`);
      return systemTimeValidator.checkFixedDateLimit();
    },

    // 检查时间戳限制
    () => {
      console.log(`[${new Date().toISOString()}] 系统模块：执行时间戳验证`);
      return systemTimeValidator.checkTimestampLimit();
    },

    // 检查当前日期是否超过2025年8月7日（系统模块版本）
    () => {
      console.log(`[${new Date().toISOString()}] 系统模块：执行当前日期验证`);
      try {
        const currentDate = new Date();

        // 解码方法名 "getFullYear", "getMonth", "getDate"
        const getFullYearMethod = String.fromCharCode(
          parseInt("1101111", 2) - 8, // g
          parseInt("1101001", 2) - 4, // e
          parseInt("1110111", 2) - 3, // t
          parseInt("1000111", 2) - 1, // F
          parseInt("1111110", 2) - 9, // u
          parseInt("1110100", 2) - 8, // l
          parseInt("1110101", 2) - 9, // l
          parseInt("1100001", 2) - 8, // Y
          parseInt("1100110", 2) - 1, // e
          parseInt("1100101", 2) - 4, // a
          parseInt("1110101", 2) - 3 // r
        );

        const getMonthMethod = String.fromCharCode(
          parseInt("1110000", 2) - 9, // g
          parseInt("1101100", 2) - 7, // e
          parseInt("1111101", 2) - 9, // t
          parseInt("1010110", 2) - 9, // M
          parseInt("1110110", 2) - 7, // o
          parseInt("1110001", 2) - 3, // n
          parseInt("1110111", 2) - 3, // t
          parseInt("1110010", 2) - 10 // h
        );

        const getDateMethod = String.fromCharCode(
          parseInt("1101110", 2) - 7, // g
          parseInt("1101101", 2) - 8, // e
          parseInt("1110111", 2) - 3, // t
          parseInt("1000111", 2) - 3, // D
          parseInt("1101000", 2) - 7, // a
          parseInt("1111100", 2) - 8, // t
          parseInt("1101111", 2) - 10 // e
        );

        const currentYear = currentDate[getFullYearMethod]();
        const currentMonth = currentDate[getMonthMethod]();
        const currentDay = currentDate[getDateMethod]();

        const isExpired =
          (currentYear & 65535) >= 2025 &&
          (currentMonth & 255) >= 8 &&
          (currentDay & 255) >= 7;

        console.log(
          `[${new Date().toISOString()}] 系统模块当前日期验证结果: ${isExpired}, 当前: ${currentYear}-${
            currentMonth + 1
          }-${currentDay}`
        );
        return isExpired;
      } catch (error) {
        console.error(
          `[${new Date().toISOString()}] 系统模块当前日期验证出错:`,
          error
        );
        return true;
      }
    },
  ];

  // 执行所有验证检查，如果任何一个返回true（超过限制），则终止系统模块
  const shouldTerminateSystem = systemTimeValidationChecks.some(
    (checkFunction) => {
      try {
        const result = checkFunction();
        console.log(
          `[${new Date().toISOString()}] 系统模块验证检查结果: ${result}`
        );
        return result;
      } catch (error) {
        console.error(
          `[${new Date().toISOString()}] 系统模块验证检查出错:`,
          error
        );
        return true; // 出错时默认终止
      }
    }
  );

  if (shouldTerminateSystem) {
    console.log(
      `[${new Date().toISOString()}] 系统模块时间验证失败，模块终止运行`
    );
    return;
  }

  console.log(`[${new Date().toISOString()}] 系统模块时间验证通过，继续运行`);

  /**
   * 硬件ID生成器
   * 生成各种伪造的硬件标识符
   */
  const hardwareIdGenerator = {
    /**
     * 生成伪造的平台UUID（macOS格式）
     * @returns {string} UUID格式的平台标识符
     */
    generatePlatformUUID: function () {
      const uuid = [8, 4, 4, 4, 12]
        .map((length) =>
          Array.from(
            { length: length },
            () => "0123456789ABCDEF"[Math.floor(16 * Math.random())]
          ).join("")
        )
        .join("-");

      console.log(`[${new Date().toISOString()}] 生成伪造平台UUID: ${uuid}`);
      return uuid;
    },

    /**
     * 生成伪造的序列号（macOS格式）
     * @returns {string} C02开头的序列号
     */
    generateSerialNumber: function () {
      const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const serialNumber =
        "C02" +
        Array.from(
          { length: 8 },
          () => chars[Math.floor(36 * Math.random())]
        ).join("");

      console.log(
        `[${new Date().toISOString()}] 生成伪造序列号: ${serialNumber}`
      );
      return serialNumber;
    },

    /**
     * 生成伪造的主板ID（macOS格式）
     * @returns {string} Mac-开头的主板标识符
     */
    generateBoardId: function () {
      const boardId =
        "Mac-" +
        Array.from(
          { length: 16 },
          () => "0123456789ABCDEF"[Math.floor(16 * Math.random())]
        ).join("");

      console.log(`[${new Date().toISOString()}] 生成伪造主板ID: ${boardId}`);
      return boardId;
    },

    /**
     * 生成伪造的机器GUID（Windows格式）
     * @returns {string} 带花括号的GUID
     */
    generateMachineGuid: function () {
      const guid = `{${[8, 4, 4, 4, 12]
        .map((length) =>
          Array.from(
            { length: length },
            () => "0123456789ABCDEF"[Math.floor(16 * Math.random())]
          ).join("")
        )
        .join("-")}}`;

      console.log(`[${new Date().toISOString()}] 生成伪造机器GUID: ${guid}`);
      return guid;
    },

    /**
     * 生成伪造的产品ID（Windows格式）
     * @returns {string} 20位字符的产品ID
     */
    generateProductId: function () {
      const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const productId = Array.from(
        { length: 20 },
        () => chars[Math.floor(36 * Math.random())]
      ).join("");

      console.log(`[${new Date().toISOString()}] 生成伪造产品ID: ${productId}`);
      return productId;
    },

    /**
     * 生成伪造的Windows序列号
     * @returns {string} 10位字符的序列号
     */
    generateWindowsSerialNumber: function () {
      const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const serialNumber = Array.from(
        { length: 10 },
        () => chars[Math.floor(36 * Math.random())]
      ).join("");

      console.log(
        `[${new Date().toISOString()}] 生成伪造Windows序列号: ${serialNumber}`
      );
      return serialNumber;
    },
  };

  // 生成所有需要的硬件标识符
  const fakePlatformUUID = hardwareIdGenerator.generatePlatformUUID();
  const fakeSerialNumber = hardwareIdGenerator.generateSerialNumber();
  const fakeBoardId = hardwareIdGenerator.generateBoardId();
  const fakeMachineGuid = hardwareIdGenerator.generateMachineGuid();
  const fakeProductId = hardwareIdGenerator.generateProductId();
  const fakeWindowsSerialNumber =
    hardwareIdGenerator.generateWindowsSerialNumber();
  /**
   * 系统信息伪造函数集合
   */
  const systemInfoSpoofer = {
    /**
     * 伪造macOS ioreg命令输出
     * @param {string} output - 原始ioreg输出
     * @returns {string} 伪造后的输出
     */
    spoofIoregOutput: function (output) {
      console.log(`[${new Date().toISOString()}] 开始伪造ioreg输出`);

      if (!output || typeof output !== "string") {
        console.log(
          `[${new Date().toISOString()}] ioreg输出为空或非字符串，直接返回`
        );
        return output;
      }

      let spoofedOutput = output;

      // 替换IOPlatformUUID
      const uuidPattern =
        /"IOPlatformUUID"\s*=\s*"[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}"/g;
      const uuidMatches = output.match(uuidPattern);
      if (uuidMatches) {
        console.log(
          `[${new Date().toISOString()}] 找到${
            uuidMatches.length
          }个IOPlatformUUID，进行替换`
        );
        spoofedOutput = spoofedOutput.replace(
          uuidPattern,
          `"IOPlatformUUID" = "${fakePlatformUUID}"`
        );
      }

      // 替换IOPlatformSerialNumber
      const serialPattern = /"IOPlatformSerialNumber"\s*=\s*"[A-Z0-9]+"/g;
      const serialMatches = output.match(serialPattern);
      if (serialMatches) {
        console.log(
          `[${new Date().toISOString()}] 找到${
            serialMatches.length
          }个IOPlatformSerialNumber，进行替换`
        );
        spoofedOutput = spoofedOutput.replace(
          serialPattern,
          `"IOPlatformSerialNumber" = "${fakeSerialNumber}"`
        );
      }

      // 替换board-id
      const boardIdPattern = /"board-id"\s*=\s*<"Mac-[0-9A-Fa-f]+">/g;
      const boardIdMatches = output.match(boardIdPattern);
      if (boardIdMatches) {
        console.log(
          `[${new Date().toISOString()}] 找到${
            boardIdMatches.length
          }个board-id，进行替换`
        );
        spoofedOutput = spoofedOutput.replace(
          boardIdPattern,
          `"board-id" = <"${fakeBoardId}">`
        );
      }

      console.log(`[${new Date().toISOString()}] ioreg输出伪造完成`);
      return spoofedOutput;
    },

    /**
     * 伪造Windows注册表命令输出
     * @param {string} output - 原始注册表输出
     * @returns {string} 伪造后的输出
     */
    spoofWindowsRegistryOutput: function (output) {
      console.log(`[${new Date().toISOString()}] 开始伪造Windows注册表输出`);

      if (!output || typeof output !== "string") {
        console.log(
          `[${new Date().toISOString()}] 注册表输出为空或非字符串，直接返回`
        );
        return output;
      }

      let spoofedOutput = output;

      // 替换MachineGuid
      const machineGuidPattern =
        /(MachineGuid\s+REG_SZ\s+)\{[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}\}/g;
      const machineGuidMatches = output.match(machineGuidPattern);
      if (machineGuidMatches) {
        console.log(
          `[${new Date().toISOString()}] 找到${
            machineGuidMatches.length
          }个MachineGuid，进行替换`
        );
        spoofedOutput = spoofedOutput.replace(
          machineGuidPattern,
          `$1${fakeMachineGuid}`
        );
      }

      // 替换ProductId
      const productIdPattern = /(ProductId\s+REG_SZ\s+)[A-Z0-9\-]+/g;
      const productIdMatches = output.match(productIdPattern);
      if (productIdMatches) {
        console.log(
          `[${new Date().toISOString()}] 找到${
            productIdMatches.length
          }个ProductId，进行替换`
        );
        spoofedOutput = spoofedOutput.replace(
          productIdPattern,
          `$1${fakeProductId}`
        );
      }

      // 替换SerialNumber
      const serialNumberPattern = /(SerialNumber\s+REG_SZ\s+)[A-Z0-9]+/g;
      const serialNumberMatches = output.match(serialNumberPattern);
      if (serialNumberMatches) {
        console.log(
          `[${new Date().toISOString()}] 找到${
            serialNumberMatches.length
          }个SerialNumber，进行替换`
        );
        spoofedOutput = spoofedOutput.replace(
          serialNumberPattern,
          `$1${fakeWindowsSerialNumber}`
        );
      }

      console.log(`[${new Date().toISOString()}] Windows注册表输出伪造完成`);
      return spoofedOutput;
    },

    /**
     * 伪造Git命令输出（返回空字符串）
     * @param {string} command - 执行的命令
     * @param {string} output - 原始输出
     * @returns {string} 伪造后的输出
     */
    spoofGitOutput: function (command, output) {
      if (output && typeof output === "string" && command.includes("git ")) {
        console.log(
          `[${new Date().toISOString()}] 拦截Git命令输出: ${command}`
        );
        return "";
      }
      return output;
    },
  };
  // 保存原始require函数（系统模块）
  const originalSystemRequire = require;

  console.log(
    `[${new Date().toISOString()}] 系统模块：设置child_process拦截器`
  );

  /**
   * 重写require函数以拦截child_process模块
   */
  require = function (moduleName) {
    console.log(`[${new Date().toISOString()}] 系统模块require: ${moduleName}`);

    const module = originalSystemRequire.apply(this, arguments);

    // 拦截child_process模块
    if (moduleName === "child_process") {
      console.log(
        `[${new Date().toISOString()}] 系统模块：拦截child_process模块`
      );

      const originalExec = module.exec;
      const originalExecSync = module.execSync;
      const originalSpawn = module.spawn;

      /**
       * 重写exec方法
       * @param {string} command - 要执行的命令
       * @param {Object} options - 执行选项
       * @param {Function} callback - 回调函数
       */
      module.exec = function (command, options, callback) {
        if (typeof command !== "string") {
          return originalExec.apply(this, arguments);
        }

        console.log(
          `[${new Date().toISOString()}] 系统模块exec拦截: ${command}`
        );

        return originalExec.call(
          this,
          command,
          options,
          function (error, stdout, stderr) {
            if (error) {
              // 对于git命令，返回空结果而不是错误
              if (command.includes("git ")) {
                console.log(
                  `[${new Date().toISOString()}] 拦截git命令错误，返回空结果`
                );
                return callback(null, "", stderr || "");
              }
              return callback(error, stdout, stderr);
            }

            if (stdout) {
              let spoofedOutput = "";

              // 根据命令类型选择相应的伪造函数
              if (command.includes("ioreg")) {
                console.log(`[${new Date().toISOString()}] 伪造ioreg命令输出`);
                spoofedOutput = systemInfoSpoofer.spoofIoregOutput(stdout);
              } else if (command.includes("git ")) {
                console.log(`[${new Date().toISOString()}] 伪造git命令输出`);
                spoofedOutput = systemInfoSpoofer.spoofGitOutput(
                  command,
                  stdout
                );
              } else if (
                command.includes("REG.exe QUERY") ||
                command.includes("reg query") ||
                command.includes("wmic") ||
                command.includes("systeminfo")
              ) {
                console.log(
                  `[${new Date().toISOString()}] 伪造Windows系统命令输出`
                );
                spoofedOutput =
                  systemInfoSpoofer.spoofWindowsRegistryOutput(stdout);
              } else {
                spoofedOutput = stdout;
              }

              callback(null, spoofedOutput, stderr);
            } else {
              callback(null, "", stderr || "");
            }
          }
        );
      };

      /**
       * 重写execSync方法
       * @param {string} command - 要执行的命令
       * @param {Object} options - 执行选项
       */
      module.execSync = function (command, options) {
        if (typeof command !== "string") {
          return originalExecSync.apply(this, arguments);
        }

        console.log(
          `[${new Date().toISOString()}] 系统模块execSync拦截: ${command}`
        );

        try {
          const result = originalExecSync.apply(this, arguments);

          if (result && result.length > 0) {
            const output = result.toString();
            let spoofedOutput = "";

            // 根据命令类型选择相应的伪造函数
            if (command.includes("ioreg")) {
              console.log(
                `[${new Date().toISOString()}] 伪造ioreg同步命令输出`
              );
              spoofedOutput = systemInfoSpoofer.spoofIoregOutput(output);
            } else if (command.includes("git ")) {
              console.log(`[${new Date().toISOString()}] 伪造git同步命令输出`);
              spoofedOutput = systemInfoSpoofer.spoofGitOutput(command, output);
            } else if (
              command.includes("REG.exe QUERY") ||
              command.includes("reg query") ||
              command.includes("wmic") ||
              command.includes("systeminfo")
            ) {
              console.log(
                `[${new Date().toISOString()}] 伪造Windows同步系统命令输出`
              );
              spoofedOutput =
                systemInfoSpoofer.spoofWindowsRegistryOutput(output);
            } else {
              spoofedOutput = output;
            }

            return Buffer.from(spoofedOutput);
          }

          return Buffer.from("");
        } catch (error) {
          // 对于git命令，返回空结果而不是抛出错误
          if (command.includes("git ")) {
            console.log(
              `[${new Date().toISOString()}] 拦截git同步命令错误，返回空结果`
            );
            return Buffer.from("");
          }
          throw error;
        }
      };

      /**
       * 重写spawn方法（保持原有行为）
       * @param {string} command - 要执行的命令
       * @param {Array} args - 命令参数
       * @param {Object} options - 执行选项
       */
      module.spawn = function (command, args, options) {
        console.log(
          `[${new Date().toISOString()}] 系统模块spawn调用: ${command}`
        );
        return originalSpawn.apply(this, arguments);
      };
    }

    return module;
  };

  console.log(`[${new Date().toISOString()}] 系统信息伪造模块初始化完成`);
})();



// 插件初始化完成日志
console.log(
  `[${new Date().toISOString()}] ========================================`
);
console.log(`[${new Date().toISOString()}] 🎉 网络请求拦截器插件初始化完成！`);
console.log(`[${new Date().toISOString()}] 📋 已启用功能：`);
console.log(
  `[${new Date().toISOString()}]   ✅ 网络请求拦截 (HTTP/HTTPS/Fetch/Axios/XMLHttpRequest)`
);
console.log(`[${new Date().toISOString()}]   ✅ 会话ID替换`);
console.log(`[${new Date().toISOString()}]   ✅ 系统信息伪造 (macOS/Windows)`);
console.log(
  `[${new Date().toISOString()}]   ✅ 命令输出拦截 (ioreg/reg/git等)`
);
console.log(`[${new Date().toISOString()}]   ✅ HTML注入悬浮窗口 (公众号展示)`);
console.log(`[${new Date().toISOString()}] 🔒 时间验证已通过，插件正常运行`);
console.log(
  `[${new Date().toISOString()}] ========================================`
);
